'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGithub } from './icons';
import { style } from 'framer-motion/client';
import { BiFontSize } from 'react-icons/bi';
import { FaTimes } from 'react-icons/fa';
import emailjs from '@emailjs/browser';
import Spline from '@splinetool/react-spline/next';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const [scrollProgress, setScrollProgress] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [message, setMessage] = useState("<PERSON> <PERSON><PERSON>, I’m interested in hiring you for a project. I’d love to discuss the details with you!");
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [toast, setToast] = useState<{ type: 'success' | 'error', msg: string } | null>(null);
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  const [name, setName] = useState("");

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      setIsScrolled(scrollY > 50);

      // Calculate scroll progress
      const progress = (scrollY / (documentHeight - windowHeight)) * 100;
      setScrollProgress(Math.min(progress, 100));

      // Detect active section
      const sections = ['home', 'about', 'portfolio', 'contact'];
      const sectionElements = sections.map(id => document.getElementById(id));

      for (let i = sections.length - 1; i >= 0; i--) {
        const element = sectionElements[i];
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= windowHeight / 2) {
            setActiveSection(sections[i]);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Call once to set initial state
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Accessibility: close on ESC
  useEffect(() => {
    if (!showModal) return;
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setShowModal(false);
    };
    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [showModal]);

  // Focus textarea on open
  useEffect(() => {
    if (showModal && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [showModal]);

  // Email validation
  const isValidEmail = (email: string) => /.+@.+\..+/.test(email);

  // EmailJS send
  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isValidEmail(email)) {
      setToast({ type: 'error', msg: 'Please enter a valid email address.' });
      return;
    }
    setLoading(true);
    try {
      await emailjs.send(
        'service_sxkg0c8',
        'template_8uf6tkv',
        {
          name,
          user_email: email,
          message,
          time: new Date().toLocaleString(),
          reply_to: email,
        },
        'QbiMivJJwMgyBsr18'
      );
      setToast({ type: 'success', msg: 'Message sent successfully!' });
      setShowModal(false);
      setEmail("");
      setName("");
      setMessage("Hi Jugal, I’m interested in hiring you for a project. I’d love to discuss the details with you!");
    } catch (err) {
      setToast({ type: 'error', msg: 'Failed to send message. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  // Hide toast after 3 seconds
  React.useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => setToast(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  const navItems = [
    { name: 'Home', href: '#home', icon: '🏠' },
    { name: 'About', href: '#about', icon: '👨‍💻' },
    { name: 'Portfolio', href: '#portfolio', icon: 'github' },
    { name: 'Contact', href: '#contact', icon: '📧' }
  ];

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      <motion.nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? 'glass-nav backdrop-blur-md'
            : 'bg-transparent'
        }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="flex items-center justify-between w-full px-4 lg:px-6 max-w-7xl mx-auto" style={{height: "100px"}}>
          {/* Logo - Left Side */}
          <motion.div
            className="text-2xl font-bold cursor-pointer flex-shrink-0"
            whileHover={{ scale: 1.05 }}
            onClick={() => scrollToSection('#home')}
          >
            <span
              className="text-white-smoke text-3xl lg:text-4xl xl:text-5xl font-extrabold"
            >
              Jugal Soni
            </span>
          </motion.div>

          {/* Navigation Links + Hire Me Button - Right Side */}
          <div className="hidden md:flex items-center space-x-2 text-xl lg:text-2xl">
            {navItems.map((item) => {
                const isActive = activeSection === item.href.replace('#', '');
                return (
                  <motion.button
                    key={item.name}
                    onClick={() => scrollToSection(item.href)}
                    className={`relative px-4 py-3 rounded-xl font-medium transition-all duration-300 flex items-center gap-2 ${
                      isActive
                        ? 'text-white-smoke bg-gradient-to-r from-royal-purple to-electric-indigo shadow-glow'
                        : 'text-text-muted hover:text-white-smoke hover:bg-white/10'
                    }`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span className={`text-xl ${(item.icon === 'github' || item.icon === '📧') ? 'icon-glow' : ''}`}>
                      {item.icon === 'github' ? (
                        <FaGithub className="w-4 h-4" />
                      ) : (
                        item.icon
                      )}
                    </span>
                    <span>{item.name}</span>
                    {isActive && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-royal-purple to-electric-indigo rounded-xl"
                        layoutId="activeNavTab"
                        initial={false}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        style={{ zIndex: -1 }}
                      />
                    )}
                  </motion.button>
                );
              })}
            <motion.button
              className="px-6 py-2 bg-gradient-to-r from-royal-purple to-electric-indigo text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-royal-purple/25 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowModal(true)}
            >
              Hire Me
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              className="md:hidden glass backdrop-blur-md border-t border-royal-purple/20"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="container mx-auto px-6 py-4 ml-30">
                <div className="flex flex-col space-y-4 ml-30">
                  {navItems.map((item, index) => (
                    <motion.button
                      key={item.name}
                      onClick={() => scrollToSection(item.href)}
                      className="text-left text-gray-300 hover:text-electric-indigo transition-colors duration-300 font-medium py-2 ml-30"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      {item.name}
                    </motion.button>
                  ))}
                  
                  <motion.button
                    className="mt-4 px-6 py-3 bg-gradient-to-r from-royal-purple to-electric-indigo text-white font-semibold rounded-lg text-center"
                    onClick={() => setShowModal(true)}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: navItems.length * 0.1 }}
                  >
                    Hire Me
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Scroll Progress Bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-royal-purple to-electric-indigo z-50 origin-left"
        style={{
          scaleX: scrollProgress / 100,
        }}
        initial={{ scaleX: 0 }}
        animate={{ scaleX: scrollProgress / 100 }}
        transition={{ duration: 0.1, ease: "easeOut" }}
      />

      {/* Enhanced scroll progress indicator */}
      {/* Removed circular percentage scroll indicator as requested */}

      {/* Modal Popup */}
      <AnimatePresence>
        {showModal && (
          <motion.div
            className="fixed inset-0 z-[99999] flex items-center justify-center bg-black/60 backdrop-blur-[8px]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            aria-modal="true"
            role="dialog"
            tabIndex={-1}
            onClick={() => setShowModal(false)}
          >
            <motion.div
              className="relative z-[100000] w-full max-w-lg mx-4 bg-gradient-to-br from-[#1a1a40]/90 to-[#2a2a60]/80 border border-royal-purple/40 rounded-3xl shadow-2xl p-8 md:p-12 neon-glow"
              initial={{ y: 80, opacity: 0, scale: 0.98 }}
              animate={{ y: 0, opacity: 1, scale: 1 }}
              exit={{ y: 80, opacity: 0, scale: 0.98 }}
              transition={{ duration: 0.4, ease: 'easeOut' }}
              onClick={e => e.stopPropagation()}
              style={{ boxShadow: '0 0 40px 8px #a855f7, 0 0 80px 16px #6366f1' }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 text-white/80 hover:text-white text-2xl focus:outline-none"
                aria-label="Close modal"
                onClick={() => setShowModal(false)}
              >
                <FaTimes />
              </button>
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-6 text-center">Ready to Work with Jugal?</h2>
              <form onSubmit={handleSend} className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-white/80 font-medium mb-2">Your Name</label>
                  <input
                    id="name"
                    type="text"
                    className="w-full rounded-xl bg-black/40 border border-royal-purple/30 text-white p-4 focus:outline-none focus:ring-2 focus:ring-electric-indigo/60"
                    value={name}
                    onChange={e => setName(e.target.value)}
                    aria-label="Your Name"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="message" className="block text-white/80 font-medium mb-2">Message</label>
                  <textarea
                    ref={textareaRef}
                    id="message"
                    className="w-full min-h-[100px] rounded-xl bg-black/40 border border-royal-purple/30 text-white p-4 focus:outline-none focus:ring-2 focus:ring-electric-indigo/60 resize-none"
                    value={message}
                    onChange={e => setMessage(e.target.value)}
                    aria-label="Message"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-white/80 font-medium mb-2">Your Email Address</label>
                  <input
                    id="email"
                    type="email"
                    className="w-full rounded-xl bg-black/40 border border-royal-purple/30 text-white p-4 focus:outline-none focus:ring-2 focus:ring-electric-indigo/60"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    aria-label="Your Email Address"
                    required
                  />
                </div>
                <button
                  type="submit"
                  className="w-full py-3 rounded-xl bg-gradient-to-r from-royal-purple to-electric-indigo text-white font-bold text-lg shadow-lg hover:scale-105 transition-all duration-300 flex items-center justify-center gap-3 disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={loading}
                  aria-busy={loading}
                >
                  {loading ? (
                    <span className="loader border-2 border-t-2 border-t-white border-white/30 rounded-full w-5 h-5 animate-spin"></span>
                  ) : (
                    'Send Message'
                  )}
                </button>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      {/* Toast Notification */}
      <AnimatePresence>
        {toast && (
          <motion.div
            className={`fixed bottom-8 left-1/2 -translate-x-1/2 z-[99999] px-6 py-4 rounded-xl font-semibold text-white shadow-lg ${toast.type === 'success' ? 'bg-green-600' : 'bg-red-600'}`}
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 40 }}
            transition={{ duration: 0.4 }}
          >
            {toast.msg}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navigation;
